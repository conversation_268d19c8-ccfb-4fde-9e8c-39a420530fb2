import { Page } from 'playwright';
import { RpaStep, ExecutionLog } from '@rpa-project/shared';
import { StepExecutionResult, RunnerContext } from '../../base';

/**
 * Conditional step executors for PlaywrightRunner
 */

export interface ConditionalExecutorContext {
  page: Page;
  variables: Record<string, any>;
  onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  executeStep: (step: RpaStep, context: RunnerContext) => Promise<StepExecutionResult>;
}

/**
 * Execute ifElementExists step
 */
export async function executeIfElementExists(
  step: RpaStep & { selector: string; thenSteps: RpaStep[]; elseSteps?: RpaStep[] },
  context: ConditionalExecutorContext
): Promise<StepExecutionResult> {
  const { page, variables, onLog, interpolateVariables, executeStep } = context;

  const interpolatedIfSelector = interpolateVariables(step.selector, variables);
  const elementExists = await page.locator(interpolatedIfSelector).count() > 0;
  const stepsToExecute = elementExists ? step.thenSteps : (step.elseSteps || []);

  onLog({
    level: 'info',
    message: `Element ${interpolatedIfSelector} ${elementExists ? 'exists' : 'does not exist'}, executing ${stepsToExecute.length} steps`,
    stepId: step.id
  });

  // Execute the conditional steps
  const runnerContext: RunnerContext = {
    variables,
    onLog,
    cancellationChecker: context.cancellationChecker
  };

  for (const conditionalStep of stepsToExecute) {
    const result = await executeStep(conditionalStep, runnerContext);
    if (!result.success) {
      return result;
    }
    if (result.variables) {
      Object.assign(variables, result.variables);
    }
  }

  return { success: true };
}

/**
 * Execute conditionalClick step
 */
export async function executeConditionalClick(
  step: RpaStep & { selector: string; condition: 'exists' | 'enabled' | 'disabled' },
  context: ConditionalExecutorContext
): Promise<StepExecutionResult> {
  const { page, variables, onLog, interpolateVariables } = context;
  const timeout = step.timeout || 30000;

  const interpolatedConditionalSelector = interpolateVariables(step.selector, variables);
  let shouldClick = false;
  let conditionResult = false;

  // Check the condition
  switch (step.condition) {
    case 'exists':
      conditionResult = await page.locator(interpolatedConditionalSelector).count() > 0;
      break;
    case 'enabled':
      const enabledElement = page.locator(interpolatedConditionalSelector);
      conditionResult = await enabledElement.count() > 0 && await enabledElement.isEnabled();
      break;
    case 'disabled':
      const disabledElement = page.locator(interpolatedConditionalSelector);
      conditionResult = await disabledElement.count() > 0 && await disabledElement.isDisabled();
      break;
    default:
      throw new Error(`Unknown condition: ${step.condition}`);
  }

  shouldClick = conditionResult;

  onLog({
    level: 'info',
    message: `Element ${interpolatedConditionalSelector} condition '${step.condition}' is ${conditionResult ? 'true' : 'false'}`,
    stepId: step.id
  });

  if (shouldClick) {
    await page.click(interpolatedConditionalSelector, { timeout });
    onLog({
      level: 'info',
      message: `Clicked element: ${interpolatedConditionalSelector}`,
      stepId: step.id
    });
  } else {
    onLog({
      level: 'info',
      message: `Skipped clicking element: ${interpolatedConditionalSelector} (condition not met)`,
      stepId: step.id
    });
  }

  return { success: true };
}
